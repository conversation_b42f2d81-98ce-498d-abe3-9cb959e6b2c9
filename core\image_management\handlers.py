"""
图片管理API处理器
处理图片管理相关的HTTP请求
"""

from typing import Dict, Any
from fastapi import HTTPException, Request
from fastapi.responses import HTMLResponse

from .services import image_service


async def get_collections_list(dataset_id: str = None) -> Dict[str, Any]:
    """获取集合列表API"""
    try:
        result = image_service.get_collections_list(dataset_id)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取集合列表失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_datasets_datas_images(
    collection_id: str,
    date_start: str = None,
    date_end: str = None,
    offset: int = 0,
    limit: int = 10,
    is_audit: int = 0
) -> Dict[str, Any]:
    """获取数据集图片数据API"""
    try:
        result = image_service.get_datasets_datas_images(
            collection_id=collection_id,
            date_start=date_start,
            date_end=date_end,
            offset=offset,
            limit=limit,
            is_audit=is_audit
        )
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "获取图片数据失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def upload_image(body: Dict[str, Any]) -> Dict[str, Any]:
    """上传图片API"""
    try:
        result = image_service.upload_image(body)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "上传图片失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def audit_image(collection_id: str) -> Dict[str, Any]:
    """审核图片API"""
    try:
        result = image_service.audit_image(collection_id)
        if result["code"] == 200:
            return result
        else:
            raise HTTPException(status_code=500, detail=result.get("message", "审核图片失败"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 保持兼容性的页面处理器（将被前端模板替代）
async def h5_images_page(request: Request) -> HTMLResponse:
    """图片管理页面（兼容性）"""
    # 重定向到新的前端结构
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>重定向中...</title>
        <script>
            window.location.href = '/#/image-management';
        </script>
    </head>
    <body>
        <p>页面重定向中，请稍候...</p>
        <p><a href="/#/image-management">如果没有自动跳转，请点击这里</a></p>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content, status_code=200)


async def h5_images_audit_page(request: Request) -> HTMLResponse:
    """图片审核页面（兼容性）"""
    # 重定向到新的前端结构
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>重定向中...</title>
        <script>
            window.location.href = '/#/audit';
        </script>
    </head>
    <body>
        <p>页面重定向中，请稍候...</p>
        <p><a href="/#/audit">如果没有自动跳转，请点击这里</a></p>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content, status_code=200)
