"""
知识库API处理器
处理知识库相关的HTTP请求
"""

import os
from typing import Dict, Any
from fastapi import HTTPException, Request
from fastapi.responses import HTMLResponse

from .services import knowledge_base_service
from .schemas import (
    DatasetResponse, CollectionListRequest, CollectionResponse,
    DataUpdateRequest, ApiResponse
)
from config.settings import settings


async def get_datasets_list() -> ApiResponse:
    """获取数据集列表"""
    try:
        datasets_list = knowledge_base_service.get_datasets_list()
        return ApiResponse(code=200, data=datasets_list)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_collection_list_info(
    keyword: str = "",
    start_date: str = "",
    end_date: str = "",
    status: str = "",
    dataset: str = ""
) -> ApiResponse:
    """获取集合列表信息"""
    try:
        collections = knowledge_base_service.get_collection_list_info(
            keyword, start_date, end_date, status, dataset
        )
        return ApiResponse(
            code=200,
            data=collections,
            server_ip=settings.TEMPLATES_IP
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_same_name_files(cpxh: str, exclude_id: str) -> ApiResponse:
    """获取相同型号的文件"""
    try:
        files = knowledge_base_service.get_same_name_files(cpxh, exclude_id)
        return ApiResponse(code=200, data=files)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def get_dataset_datas(collection_id: str) -> ApiResponse:
    """获取数据集数据"""
    try:
        data = knowledge_base_service.get_dataset_datas(collection_id)
        return ApiResponse(code=200, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


async def index(request: Request) -> HTMLResponse:
    """主页处理器 - 优先使用新的模块化前端结构"""
    # 确保全局数据已加载
    knowledge_base_service.get_global_data()

    # 优先使用新的前端结构
    frontend_path = os.path.join("frontend", "index.html")

    try:
        if os.path.exists(frontend_path):
            with open(frontend_path, "r", encoding="utf-8") as file:
                html_content = file.read()
            print("✓ 使用新的模块化前端结构")
        else:
            # 如果新结构不存在，创建一个基本的重定向页面
            html_content = """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>知识库管理系统</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .container { max-width: 600px; margin: 0 auto; }
                    .error { color: #F56C6C; }
                    .info { color: #409EFF; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>知识库管理系统</h1>
                    <p class="error">新的模块化前端结构未找到</p>
                    <p class="info">请确保前端重构已完成</p>
                    <p>
                        <a href="/media/index.html">使用原有结构</a> |
                        <a href="javascript:location.reload()">刷新页面</a>
                    </p>
                </div>
            </body>
            </html>
            """
            print("✗ 新前端结构不存在，显示错误页面")
    except Exception as e:
        print(f"✗ 加载前端页面失败: {e}")
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head><title>知识库管理系统 - 错误</title></head>
        <body>
            <div style="text-align: center; padding: 50px;">
                <h1>系统错误</h1>
                <p>无法加载前端页面: {e}</p>
                <p><a href="javascript:location.reload()">重试</a></p>
            </div>
        </body>
        </html>
        """

    return HTMLResponse(content=html_content, status_code=200)
